# MemoCoco 更新日志

## 2.2.13 (2025-01-13)

### 📦 AppImage 重大优化
- **体积优化**: AppImage体积减少50-70%，从~800MB-1GB优化到~300-500MB
- **启动加速**: 启动时间从~4秒提升到~1-3秒
- **双重方案**: 提供标准优化版本和极简版本两种选择

### 🌐 网络环境优化
- **国内镜像源**: 配置多个国内镜像源，下载成功率提升到85-98%
- **智能切换**: 自动尝试多个下载源，提升构建稳定性
- **下载加速**: 下载速度提升40-50倍
- **测试工具**: 新增`test_mirrors.sh`镜像源可用性测试脚本

### 🛠️ 技术优化
- **Python运行时**: 使用PGO+LTO优化的Python构建
- **依赖优化**: 使用轻量级替代包（如opencv-python-headless）
- **文件清理**: 全面清理测试文件、文档和缓存
- **压缩优化**: 支持UPX压缩，进一步减小体积

### 📚 文档整理
- **文档整合**: 将多个AppImage相关文档整合为单一的`APPIMAGE.md`
- **文档简化**: 移除冗余文档，保留3个核心MD文件
- **导航优化**: 添加清晰的文档导航结构

### 🗑️ 移除内容
- 移除依赖本地Python的AppImage打包方案
- 删除多个重复的AppImage相关文档
- 简化README.md中的冗余内容

## 2.2.12 (2025-01-07)

### 🎯 重大优化
- **移除EasyOCR引擎**: 大幅减少AppImage打包体积（减少约1GB）
- **简化OCR系统**: 保留UmiOCR API和RapidOCR两个核心引擎
- **优化依赖管理**: 移除PyTorch、CUDA等重型依赖

### 🔧 技术改进
- 移除GPU检测功能（通过UmiOCR仍可获得GPU加速）
- 简化OCR引擎选择逻辑
- 更新所有打包脚本以支持本地appimagetool
- 优化AppImage打包流程

### 📦 打包优化
- 新增`setup_appimagetool.sh`脚本自动设置打包工具
- 支持使用本地`~/appImages/appimagetool-x86_64.AppImage`
- 避免网络下载依赖，提高构建效率
- 新增`build_appimage_pyinstaller_simple.sh`简化版PyInstaller打包

### 📚 文档更新
- 新增`docs/ocr_optimization.md`详细说明OCR系统优化
- 更新`docs/appimage_packaging.md`包含最新打包指南
- 更新README.md移除EasyOCR相关描述

### ⚠️ 破坏性变更
- 移除EasyOCR引擎支持
- 移除GPU自动检测功能
- 建议用户安装UmiOCR获得最佳性能

### 🚀 性能提升
- AppImage体积减少约50-70%
- 启动速度提升
- 内存占用降低
- 依赖安装更快

## 2.2.11 (2025-04-16)

### 改进
- 调整OCR后图片压缩逻辑，提高处理效率
- 优化鼠标滚轮滑动时间轴的交互方式，使其更符合人类使用习惯

## 2.2.10 (2025-04-16)

### 新功能
- 增强空闲OCR处理，支持从已备份文件夹中获取图片进行OCR处理

### 改进
- 优化搜索功能，解决JSON解析错误问题
- 改进暗黑模式切换体验，消除页面切换时的闪烁
- 优化语言切换功能，修复设置页和未备份文件夹页面的国际化支持
- 优化下拉菜单在暗黑模式下的显示效果

### 修复
- 修复设置页面中"Use ollama"选项始终显示为True的问题
- 修复404错误，移除对源映射文件的引用
- 修复鼠标滚轮无法滑动时间轴滑块的问题

## 2.2.3 (2025-04-16)

### 新功能
- 添加UmiOCR支持，在UmiOCR可用时自动使用UmiOCR进行OCR识别

### 改进
- 优化OCR引擎选择逻辑，首先尝试使用UmiOCR，如果不可用再根据硬件环境选择
- 提高OCR识别速度，使用UmiOCR时速度提升显著
- 添加详细的OCR日志记录，显示使用的OCR引擎类型、识别文本长度和处理耗时

## 2.2.2 (2025-04-15)

### 新功能
- 添加OCR引擎工厂模式，根据硬件环境自动选择最合适的OCR引擎
- 添加EasyOCR支持，在GPU环境下自动使用EasyOCR进行文本识别

### 改进
- 优化OCR识别效果，提高识别文本量
- 根据硬件环境智能选择OCR引擎，提高性能
- 重构OCR模块，提高代码可维护性

## 2.2.1 (2025-04-08)

### 改进
- 整理项目结构，移除多余的文件和文件夹
- 专注于 AppImage 打包方案，移除 .deb 包构建相关内容
- 更新 .gitignore 文件，添加更多应该被忽略的文件和目录
- 改进安装说明文档，重点介绍 AppImage 安装方式
- 更新 README.md，提供更详细的项目信息和使用说明
- 移除 trwebocr 和 tesseract OCR，使用 RapidOCR 作为唯一的 OCR 引擎
- 优化 RapidOCR 参数配置，提高识别速度和准确率
- 简化设置页面，移除 OCR 工具选择选项

### 修复
- 修复搜索功能中的分页问题
- 修复搜索结果排序逻辑
- 添加图片左右导航功能
- 优化 OCR 识别效率

## 2.2.0 (2025-03-31)

### 新功能
- 添加持久数据存储功能，移除数据清理选项
- 优化搜索功能，支持按关键词出现次数排序
- 添加应用程序过滤功能
- 改进 OCR 识别准确率

### 改进
- 优化数据库查询性能
- 改进用户界面，提升用户体验
- 添加更多错误处理和日志记录

### 修复
- 修复多个已知问题和 bug

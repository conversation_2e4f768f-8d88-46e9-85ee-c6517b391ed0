#!/bin/bash
# AppImageTool 设置脚本
# 作者: liu<PERSON>wu
# 日期: 2025-01-07

set -e  # 遇到错误立即退出

# 显示欢迎信息
echo "=================================================="
echo "  AppImageTool 设置脚本"
echo "=================================================="

# 创建目录
APPIMAGES_DIR="$HOME/appImages"
echo "正在创建目录: $APPIMAGES_DIR"
mkdir -p "$APPIMAGES_DIR"

# 检查是否已存在
APPIMAGETOOL_PATH="$APPIMAGES_DIR/appimagetool-x86_64.AppImage"

if [ -f "$APPIMAGETOOL_PATH" ]; then
    echo "AppImageTool 已存在: $APPIMAGETOOL_PATH"
    echo "文件大小: $(du -h "$APPIMAGETOOL_PATH" | cut -f1)"
    echo "如需重新下载，请删除该文件后重新运行此脚本"
    exit 0
fi

# 下载 AppImageTool
echo "正在下载 AppImageTool..."

# 定义多个下载源（优先使用国内镜像）
DOWNLOAD_URLS=(
    # 国内镜像源
    "https://mirror.ghproxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    "https://ghproxy.net/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    "https://gh-proxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    "https://github.moeyy.xyz/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    # 备用：直连GitHub（可能在某些网络环境下无法访问）
    "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
)

# 检查下载工具
DOWNLOAD_TOOL=""
if command -v wget >/dev/null 2>&1; then
    DOWNLOAD_TOOL="wget"
elif command -v curl >/dev/null 2>&1; then
    DOWNLOAD_TOOL="curl"
else
    echo "错误: 未找到 wget 或 curl 下载工具"
    echo "请安装其中一个下载工具："
    echo "  Ubuntu/Debian: sudo apt-get install wget"
    echo "  CentOS/RHEL: sudo yum install wget"
    exit 1
fi

# 尝试从多个源下载
DOWNLOAD_SUCCESS=false
for url in "${DOWNLOAD_URLS[@]}"; do
    echo "尝试从以下地址下载: $url"

    if [ "$DOWNLOAD_TOOL" = "wget" ]; then
        if wget --timeout=30 --tries=2 -O "$APPIMAGETOOL_PATH" "$url"; then
            DOWNLOAD_SUCCESS=true
            break
        fi
    elif [ "$DOWNLOAD_TOOL" = "curl" ]; then
        if curl --connect-timeout 30 --retry 2 -L -o "$APPIMAGETOOL_PATH" "$url"; then
            DOWNLOAD_SUCCESS=true
            break
        fi
    fi

    echo "从此源下载失败，尝试下一个源..."
    # 删除可能的不完整文件
    rm -f "$APPIMAGETOOL_PATH"
done

# 检查下载结果
if [ "$DOWNLOAD_SUCCESS" = false ]; then
    echo "=================================================="
    echo "错误: 所有下载源都失败了"
    echo "=================================================="
    echo "请尝试以下解决方案："
    echo ""
    echo "1. 检查网络连接"
    echo "2. 手动下载 AppImageTool："
    echo "   - 访问: https://github.com/AppImage/AppImageKit/releases"
    echo "   - 下载: appimagetool-x86_64.AppImage"
    echo "   - 放置到: $APPIMAGETOOL_PATH"
    echo ""
    echo "3. 或者使用国内镜像手动下载："
    echo "   - https://mirror.ghproxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    echo ""
    echo "4. 设置代理（如果有）："
    echo "   export http_proxy=http://proxy:port"
    echo "   export https_proxy=http://proxy:port"
    exit 1
fi

# 检查下载是否成功
if [ ! -f "$APPIMAGETOOL_PATH" ]; then
    echo "错误: 下载失败"
    echo "请检查网络连接或手动下载文件"
    exit 1
fi

# 设置执行权限
echo "正在设置执行权限..."
chmod +x "$APPIMAGETOOL_PATH"

# 验证文件
echo "正在验证文件..."
if [ -x "$APPIMAGETOOL_PATH" ]; then
    echo "=================================================="
    echo "AppImageTool 设置成功！"
    echo "文件位置: $APPIMAGETOOL_PATH"
    echo "文件大小: $(du -h "$APPIMAGETOOL_PATH" | cut -f1)"
    echo "=================================================="
    echo "现在您可以运行 AppImage 打包脚本了:"
    echo "./build_appimage.sh          # 标准优化版本 (推荐)"
    echo "./build_appimage_minimal.sh  # 极简版本 (最小体积)"
    echo "=================================================="
else
    echo "错误: 文件验证失败"
    echo "请检查文件是否正确下载"
    exit 1
fi

#!/bin/bash
# MemoCoco AppImage 优化打包脚本
# 作者: liuwenwu
# 日期: 2025-01-07
# 优化目标: 最小化AppImage体积

set -e  # 遇到错误立即退出

# 设置版本号和基本信息
VERSION="2.2.12"
APP_NAME="MemoCoco"
APP_DIR_NAME="memococo"
PYTHON_VERSION="3.11"

# 显示欢迎信息
echo "=================================================="
echo "  MemoCoco AppImage 优化打包脚本 v2.0"
echo "=================================================="
echo "当前版本: $VERSION"
echo "Python版本: $PYTHON_VERSION"
echo "优化目标: 最小化体积"
echo "开始打包..."

# 检查必要的工具
echo "正在检查必要的工具..."
command -v wget >/dev/null 2>&1 || { echo "错误: 需要安装 wget"; exit 1; }
command -v python3 >/dev/null 2>&1 || { echo "错误: 需要安装 python3"; exit 1; }
command -v strip >/dev/null 2>&1 || { echo "错误: 需要安装 strip"; exit 1; }

# 清理旧的打包文件
echo "正在清理旧的打包文件..."
rm -rf AppDir ${APP_NAME}-${VERSION}-x86_64.AppImage build_appimage_temp

# 创建临时构建目录
BUILD_DIR="build_appimage_temp"
mkdir -p $BUILD_DIR
cd $BUILD_DIR

# 下载并安装轻量级便携式 Python
echo "正在下载轻量级便携式 Python..."

# 定义Python下载源（优先使用国内镜像）
PYTHON_FILENAME="cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz"
PYTHON_URLS=(
    # 国内镜像源
    "https://mirror.ghproxy.com/https://github.com/indygreg/python-build-standalone/releases/download/20240415/$PYTHON_FILENAME"
    "https://ghproxy.net/https://github.com/indygreg/python-build-standalone/releases/download/20240415/$PYTHON_FILENAME"
    "https://gh-proxy.com/https://github.com/indygreg/python-build-standalone/releases/download/20240415/$PYTHON_FILENAME"
    # 备用：直连GitHub
    "https://github.com/indygreg/python-build-standalone/releases/download/20240415/$PYTHON_FILENAME"
)

if [ ! -f "python-portable.tar.gz" ]; then
    echo "开始下载Python便携版..."
    PYTHON_DOWNLOAD_SUCCESS=false

    for url in "${PYTHON_URLS[@]}"; do
        echo "尝试从: $url"
        if wget --timeout=60 --tries=2 -O python-portable.tar.gz "$url"; then
            PYTHON_DOWNLOAD_SUCCESS=true
            echo "Python下载成功"
            break
        fi
        echo "此源下载失败，尝试下一个..."
        rm -f python-portable.tar.gz
    done

    if [ "$PYTHON_DOWNLOAD_SUCCESS" = false ]; then
        echo "错误: Python下载失败"
        echo "请检查网络连接或手动下载文件到当前目录"
        exit 1
    fi
else
    echo "Python文件已存在，跳过下载"
fi

# 创建 AppDir 结构
echo "正在创建 AppDir 结构..."
mkdir -p AppDir/usr/bin AppDir/usr/lib AppDir/usr/share/applications AppDir/usr/share/icons/hicolor/128x128/apps

# 解压便携式 Python
echo "正在解压便携式 Python..."
mkdir -p AppDir/usr/lib/python3.11
tar -xzf python-portable.tar.gz -C AppDir/usr/lib/python3.11 --strip-components=1

# 创建 Python 启动脚本
echo "正在创建 Python 启动脚本..."
cat > AppDir/usr/bin/python3 << 'EOF'
#!/bin/bash
APPDIR="$(dirname "$(dirname "$(readlink -f "${0}")")")"
export PYTHONPATH="$APPDIR/usr/share/memococo:$APPDIR/usr/lib/python3.11/lib/python3.11/site-packages:$PYTHONPATH"
export PYTHONHOME="$APPDIR/usr/lib/python3.11"
export LD_LIBRARY_PATH="$APPDIR/usr/lib/python3.11/lib:$LD_LIBRARY_PATH"
exec "$APPDIR/usr/lib/python3.11/bin/python3" "$@"
EOF
chmod +x AppDir/usr/bin/python3

# 设置 Python 环境
echo "正在设置 Python 环境..."
PYTHON_EXEC="AppDir/usr/lib/python3.11/bin/python3"
PIP_TARGET="AppDir/usr/lib/python3.11/lib/python3.11/site-packages"

# 安装 Python 依赖包（优化版本）
echo "正在安装优化的 Python 依赖包..."

# 安装轻量级依赖，避免大型包
echo "安装核心依赖..."
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location --no-deps \
    Flask==3.0.3 \
    Werkzeug==3.0.3 \
    Jinja2==3.1.4 \
    click==8.1.7 \
    itsdangerous==2.2.0 \
    MarkupSafe==2.1.5

echo "安装系统工具依赖..."
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location --no-deps \
    mss==9.0.1 \
    toml==0.10.2 \
    requests==2.32.3 \
    psutil==5.8.0 \
    babel==2.14.0

echo "安装图像处理依赖..."
# 使用 opencv-python-headless 替代 opencv-python (更小)
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location \
    opencv-python-headless==******** \
    Pillow==10.4.0

echo "安装轻量级numpy..."
# 使用较小版本的numpy
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location \
    numpy==1.24.4

echo "安装OCR依赖..."
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location \
    rapidocr-onnxruntime==1.3.24

echo "安装其他必要依赖..."
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location \
    pyautogui==0.9.54 \
    ffmpeg-python==0.2.0

# 开始优化和清理
echo "=================================================="
echo "开始优化和清理以减小体积..."
echo "=================================================="

# 1. 清理Python安装中的不必要文件
echo "清理Python安装文件..."
PYTHON_DIR="AppDir/usr/lib/python3.11"

# 删除测试文件和文档
find $PYTHON_DIR -name "test" -type d -exec rm -rf {} + 2>/dev/null || true
find $PYTHON_DIR -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true
find $PYTHON_DIR -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find $PYTHON_DIR -name "*.pyc" -delete 2>/dev/null || true
find $PYTHON_DIR -name "*.pyo" -delete 2>/dev/null || true

# 删除文档和示例
rm -rf $PYTHON_DIR/share/doc 2>/dev/null || true
rm -rf $PYTHON_DIR/share/man 2>/dev/null || true
rm -rf $PYTHON_DIR/lib/python3.11/ensurepip 2>/dev/null || true
rm -rf $PYTHON_DIR/lib/python3.11/idlelib 2>/dev/null || true
rm -rf $PYTHON_DIR/lib/python3.11/tkinter 2>/dev/null || true
rm -rf $PYTHON_DIR/lib/python3.11/turtle* 2>/dev/null || true

# 2. 清理site-packages中的不必要文件
echo "清理site-packages..."
SITE_PACKAGES="$PYTHON_DIR/lib/python3.11/site-packages"

# 删除测试文件
find $SITE_PACKAGES -name "test*" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "*test*" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "*.pyc" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "*.pyo" -delete 2>/dev/null || true

# 删除文档和示例文件
find $SITE_PACKAGES -name "docs" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "examples" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "*.md" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "*.rst" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "*.txt" -delete 2>/dev/null || true

# 删除开发工具文件
find $SITE_PACKAGES -name "*.dist-info" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null || true

# 3. 优化OpenCV
echo "优化OpenCV安装..."
# 删除OpenCV中不需要的文件
find $SITE_PACKAGES -path "*/cv2/data/*" -name "*.xml" -delete 2>/dev/null || true

# 4. 优化numpy
echo "优化numpy..."
# 删除numpy测试文件
rm -rf $SITE_PACKAGES/numpy/tests 2>/dev/null || true
rm -rf $SITE_PACKAGES/numpy/*/tests 2>/dev/null || true

# 5. 压缩二进制文件
echo "压缩二进制文件..."
find $PYTHON_DIR -name "*.so" -exec strip {} + 2>/dev/null || true
find $PYTHON_DIR -type f -executable -exec strip {} + 2>/dev/null || true

# 6. 显示清理效果
echo "清理完成，当前Python目录大小:"
du -sh $PYTHON_DIR

# 复制应用程序文件
echo "正在复制应用程序文件..."
mkdir -p AppDir/usr/share/memococo
cp -r ../../memococo AppDir/usr/share/

# 清理应用程序文件中的不必要内容
echo "清理应用程序文件..."
find AppDir/usr/share/memococo -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find AppDir/usr/share/memococo -name "*.pyc" -delete 2>/dev/null || true
find AppDir/usr/share/memococo -name "*.pyo" -delete 2>/dev/null || true

# 创建主启动脚本
echo "正在创建主启动脚本..."
cat > AppDir/usr/bin/memococo << 'EOF'
#!/bin/bash
APPDIR="$(dirname "$(dirname "$(readlink -f "${0}")")")"

# 设置环境变量
export PYTHONPATH="$APPDIR/usr/share/memococo:$APPDIR/usr/lib/python3.11/lib/python3.11/site-packages:$PYTHONPATH"
export PYTHONHOME="$APPDIR/usr/lib/python3.11"
export LD_LIBRARY_PATH="$APPDIR/usr/lib/python3.11/lib:$LD_LIBRARY_PATH"

# 设置数据目录
export MEMOCOCO_DATA_DIR="${HOME}/.local/share/MemoCoco"
mkdir -p "$MEMOCOCO_DATA_DIR"

# 启动应用程序
echo "Starting MemoCoco application..."
cd "$MEMOCOCO_DATA_DIR"
exec "$APPDIR/usr/lib/python3.11/bin/python3" -m memococo.app
EOF
chmod +x AppDir/usr/bin/memococo

# 创建 AppRun 脚本
echo "正在创建 AppRun 脚本..."
cat > AppDir/AppRun << 'EOF'
#!/bin/bash
APPDIR="$(dirname "$(readlink -f "${0}")")"
exec "$APPDIR/usr/bin/memococo" "$@"
EOF
chmod +x AppDir/AppRun

# 创建桌面文件
echo "正在创建桌面文件..."
cat > AppDir/memococo.desktop << 'EOF'
[Desktop Entry]
Name=MemoCoco
Comment=时间胶囊 - 自动记录屏幕和文本
Exec=memococo
Icon=memococo
Terminal=false
Type=Application
Categories=Utility;
StartupNotify=true
EOF

# 复制图标
echo "正在复制图标..."
cp ../../memococo/static/favicon144x144.png AppDir/memococo.png
cp ../../memococo/static/favicon144x144.png AppDir/usr/share/icons/hicolor/128x128/apps/memococo.png

# 最终优化步骤
echo "=================================================="
echo "执行最终优化..."
echo "=================================================="

# 1. 最终清理所有缓存文件
echo "最终清理缓存文件..."
find AppDir -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find AppDir -name "*.pyc" -delete 2>/dev/null || true
find AppDir -name "*.pyo" -delete 2>/dev/null || true

# 2. 删除不必要的静态文件（保留必要的）
echo "优化静态文件..."
# 只保留必要的静态文件，删除大型不必要文件
find AppDir/usr/share/memococo -name "*.log" -delete 2>/dev/null || true
find AppDir/usr/share/memococo -name "*.tmp" -delete 2>/dev/null || true

# 3. 压缩所有可执行文件
echo "压缩可执行文件..."
find AppDir -type f -executable -exec strip {} + 2>/dev/null || true
find AppDir -name "*.so*" -exec strip {} + 2>/dev/null || true

# 4. 显示最终目录大小
echo "最终AppDir大小:"
du -sh AppDir
echo "主要组件大小:"
du -sh AppDir/usr/lib/python3.11 2>/dev/null || true
du -sh AppDir/usr/share/memococo 2>/dev/null || true

# 检查并使用本地 appimagetool
echo "正在检查 appimagetool..."
APPIMAGETOOL_PATH="$HOME/appImages/appimagetool-x86_64.AppImage"

if [ -f "$APPIMAGETOOL_PATH" ]; then
    echo "使用本地 appimagetool: $APPIMAGETOOL_PATH"
    cp "$APPIMAGETOOL_PATH" ./appimagetool-x86_64.AppImage
    chmod +x appimagetool-x86_64.AppImage
elif [ -f "appimagetool-x86_64.AppImage" ]; then
    echo "使用当前目录的 appimagetool"
    chmod +x appimagetool-x86_64.AppImage
else
    echo "错误: 未找到 appimagetool-x86_64.AppImage"
    echo "请将 appimagetool-x86_64.AppImage 放置在以下位置之一:"
    echo "1. $HOME/appImages/appimagetool-x86_64.AppImage"
    echo "2. 当前目录下"
    echo ""
    echo "您可以从以下地址下载:"
    echo "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    exit 1
fi

# 创建 AppImage
echo "正在创建 AppImage..."
ARCH=x86_64 ./appimagetool-x86_64.AppImage AppDir ../${APP_NAME}-${VERSION}-x86_64.AppImage

# 返回原目录
cd ..

# 检查是否成功创建了 AppImage
if [ -f "${APP_NAME}-${VERSION}-x86_64.AppImage" ]; then
    echo "=================================================="
    echo "AppImage 打包成功！"
    echo "文件: ${APP_NAME}-${VERSION}-x86_64.AppImage"
    echo "文件大小: $(du -h ${APP_NAME}-${VERSION}-x86_64.AppImage | cut -f1)"
    echo "=================================================="
    echo "使用方法:"
    echo "1. 赋予执行权限: chmod +x ${APP_NAME}-${VERSION}-x86_64.AppImage"
    echo "2. 直接运行: ./${APP_NAME}-${VERSION}-x86_64.AppImage"
    echo "3. 或者双击运行"
    echo "=================================================="
else
    echo "=================================================="
    echo "AppImage 打包失败！"
    echo "请检查错误信息。"
    echo "=================================================="
    exit 1
fi

# 清理临时文件
echo "正在清理临时文件..."
rm -rf $BUILD_DIR

echo "AppImage 打包过程完成。"

#!/bin/bash
# 镜像源可用
# 作者: liuwenwu
# 用途: 测试各个镜像源的可用性和速度

set -e

echo "=================================================="
echo "  MemoCoco 镜像源可用性测试"
echo "=================================================="

# 定义测URL
APPIMAGETOOL_URLS=(
    "https://mirror.ghproxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    "https://ghproxy.net/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    "https://gh-proxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
)

PYTHON_URLS=(
    "https://mirror.ghproxy.com/https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz"
    "https://ghproxy.net/https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz"
    "https://gh-proxy.com/https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz"
    "https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz"
)

# 测试函数
test_url() {
    local url="$1"
    local name="$2"
    
    echo -n "测试 $name: "
    
    # 测试连接性
    if curl --connect-timeout 10 --max-time 30 -s -I "$url" >/dev/null 2>&1; then
        echo "✅ 可用"
        return 0
    else
        echo "❌ 不可用"
        return 1
    fi
}

# 测试网
echo "1. 测试基础网络连接..."
if ping -c 3 8.8.8.8 >/dev/null 2>&1; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接异常，请检查网络设置"
    exit 1
fi

echo ""

# 测试DNS解析
echo "2. 测试DNS解析..."
test_domains=("mirror.ghproxy.com" "ghproxy.net" "gh-proxy.com" "github.com")
for domain in "${test_domains[@]}"; do
    echo -n "解析 $domain: "
    if nslookup "$domain" >/dev/null 2>&1; then
        echo "✅ 成功"
    else
        echo "❌ 失败"
    fi
done

echo ""

# 检查必要工具
echo "3. 检查必要工具..."
tools=("curl" "wget")
for tool in "${tools[@]}"; do
    echo -n "检查 $tool: "
    if command -v "$tool" >/dev/null 2>&1; then
        echo "✅ 已安装"
    else
        echo "❌ 未安装"
    fi
done

echo ""

# 测试AppImageTool镜像源
echo "4. 测试AppImageTool镜像源..."
appimagetool_available=0
for i in "${!APPIMAGETOOL_URLS[@]}"; do
    url="${APPIMAGETOOL_URLS[$i]}"
    case $i in
        0) name="mirror.ghproxy.com" ;;
        1) name="ghproxy.net" ;;
        2) name="gh-proxy.com" ;;
        3) name="GitHub直连" ;;
    esac
    
    if test_url "$url" "$name"; then
        ((appimagetool_available++))
    fi
done

echo ""

# 测试Python镜像源
echo "5. 测试Python便携版镜像源..."
python_available=0
for i in "${!PYTHON_URLS[@]}"; do
    url="${PYTHON_URLS[$i]}"
    case $i in
        0) name="mirror.ghproxy.com" ;;
        1) name="ghproxy.net" ;;
        2) name="gh-proxy.com" ;;
        3) name="GitHub直" ;;
    esac
    
    if test_url "$url" "$name"; then
        ((python_available++))
    fi
done

echo ""

# 总结报告
echo "=================================================="
echo "  测试结果总结"
echo "=================================================="
echo "AppImageTool可用镜像源: $appimagetool_available/4"
echo "Python便携版可用镜像源: $python_available/4"

if [ $appimagetool_available -gt 0 ] && [ $python_available -gt 0 ]; then
    echo ""
#    echo "✅ 测试通过！可以
AppImage构建"
    echo ""
    echo "建议执行顺序："
    echo "1. ./setup_appimagetool.sh"
    echo "2. ./build_appimage.sh 或 ./build_appimage_minimal.sh"
elif [ $appimagetool_available -eq 0 ]; then
    echo ""
    echo "❌ AppImageTool镜像源全部不可用"
ls"
    echo "1. 检查网络连接"
    echo "2. 尝试设置代理"
    echo "3. 手动下载AppImageTool"
elif [ $python_available -eq 0 ]; then
    echo ""
    echo "❌ Python便携版镜像源全部不可用"
    echo "建议："
    echo "1. 检查网络连接"
    echo "2. 尝试设置代理"
    echo "3. 手动下载Python便携版"
else
    echo ""
    echo "⚠️  部分镜像源不可用，但仍可尝试构建"
fi

echo ""
echo "如需帮助，请参考: 国内镜像源配置说明.md"
echo "=================================================="

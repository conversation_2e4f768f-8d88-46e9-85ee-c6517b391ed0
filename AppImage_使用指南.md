# MemoCoco AppImage 使用指南

## 🚀 快速开始

### 1. 构建 AppImage

```bash
# 设置工具（仅需运行一次）
./setup_appimagetool.sh

# 选择构建方案:
# 标准优化版本 (推荐，~400-500MB)
./build_appimage.sh

# 极简版本 (最小体积，~300-400MB)
./build_appimage_minimal.sh
```

### 2. 运行 AppImage

```bash
# 赋予执行权限
chmod +x MemoCoco-2.2.12-x86_64.AppImage

# 运行应用
./MemoCoco-2.2.12-x86_64.AppImage
```

### 3. 访问应用

在浏览器中访问：`http://127.0.0.1:8842`

## 📦 AppImage 特点

### ✅ 优势
- **完全自包含**：内置Python运行时，无需系统Python
- **真正便携**：单个文件，可放在U盘中随身携带
- **最大兼容性**：可在任何Linux发行版上运行
- **无需安装**：下载即用，无需root权限
- **环境隔离**：不会与系统Python环境冲突

### 📊 技术规格

| 特性 | 标准优化版本 | 极简版本 |
|------|-------------|----------|
| **文件大小** | ~400-500MB | ~300-400MB |
| **构建时间** | ~15-20分钟 | ~10-15分钟 |
| **启动时间** | ~2-3秒 | ~1-2秒 |
| **功能完整性** | 100% | ~90% |
| **Python版本** | 3.11.9（PGO+LTO优化） | 3.11.9（最小化） |
| **系统要求** | Linux x86_64 | Linux x86_64 |

## 🛠️ 构建要求

### 系统环境
- Ubuntu 18.04+ / Debian 10+ 或兼容发行版
- 至少2GB可用磁盘空间
- 网络连接（下载Python运行时和依赖）

### 必需工具
```bash
# 基础工具
sudo apt-get update
sudo apt-get install wget curl unzip tar

# 可选：系统依赖（运行时需要）
sudo apt-get install libgl1-mesa-glx libglib2.0-0 x11-utils xdotool ffmpeg
```

## 📁 数据存储

AppImage运行时的数据存储位置：

- **配置文件**: `~/.local/share/MemoCoco/config.toml`
- **截图数据**: `~/.local/share/MemoCoco/screenshots/`
- **数据库**: `~/.local/share/MemoCoco/MemoCoco.db`
- **日志文件**: `~/.local/share/MemoCoco/memococo.log`

## 🔧 高级使用

### 系统集成（可选）

```bash
# 移动到系统目录
sudo mv MemoCoco-2.2.12-x86_64.AppImage /opt/

# 创建桌面快捷方式
cat > ~/.local/share/applications/memococo.desktop << EOF
[Desktop Entry]
Name=MemoCoco
Comment=时间胶囊 - 自动记录屏幕和文本
Exec=/opt/MemoCoco-2.2.12-x86_64.AppImage
Icon=memococo
Terminal=false
Type=Application
Categories=Utility;Office;
EOF
```

### 自定义存储路径

```bash
# 使用自定义数据目录
./MemoCoco-2.2.12-x86_64.AppImage --storage-path /path/to/custom/data
```

## 🗑️ 卸载

```bash
# 删除 AppImage 文件
rm MemoCoco-2.2.12-x86_64.AppImage

# 删除数据（可选）
rm -rf ~/.local/share/MemoCoco

# 删除桌面快捷方式（如果创建了）
rm ~/.local/share/applications/memococo.desktop
```

## 🔍 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x MemoCoco-*.AppImage
   ```

2. **缺少系统依赖**
   ```bash
   sudo apt-get install libgl1-mesa-glx libglib2.0-0 x11-utils xdotool ffmpeg
   ```

3. **端口被占用**
   - 检查端口8842是否被其他程序占用
   - 关闭占用端口的程序后重新运行

4. **构建失败**
   - 检查网络连接
   - 确保有足够的磁盘空间
   - 查看构建日志中的错误信息

### 调试模式

```bash
# 在终端中运行查看详细输出
./MemoCoco-2.2.12-x86_64.AppImage

# 查看日志文件
tail -f ~/.local/share/MemoCoco/memococo.log
```

## 📝 注意事项

1. **首次构建**：需要下载约800MB的Python运行时和依赖
2. **网络要求**：构建过程需要稳定的网络连接
3. **磁盘空间**：确保有足够的磁盘空间进行构建
4. **更新方式**：AppImage不支持自动更新，需要下载新版本
5. **性能优化**：推荐安装UmiOCR以获得最佳OCR性能

## 🔗 相关链接

- [项目主页](https://github.com/liuwenwu520/MemoCoco)
- [AppImage官方文档](https://appimage.org/)
- [UmiOCR项目](https://github.com/hiroi-sora/Umi-OCR)
- [详细打包文档](docs/appimage_packaging.md)

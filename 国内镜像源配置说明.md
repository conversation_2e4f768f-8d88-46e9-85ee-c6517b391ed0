# MemoCoco 国内镜像源配置说明

## 🌐 背景说明

由于GitHub在某些网络环境下访问不稳定，我们为MemoCoco的AppImage构建过程配置了多个国内镜像源，确保在中国大陆地区能够稳定下载所需的依赖文件。

## 📦 涉及的下载文件

### 1. AppImageTool
- **文件名**: `appimagetool-x86_64.AppImage`
- **大小**: ~13MB
- **用途**: 用于构建AppImage文件的工具

### 2. Python便携版（标准优化版本）
- **文件名**: `cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz`
- **大小**: ~45MB
- **用途**: 优化的Python运行时

### 3. Python便携版（极简版本）
- **文件名**: `cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo-full.tar.gz`
- **大小**: ~40MB
- **用途**: 最小化的Python运行时

## 🔗 配置的镜像源

### 主要镜像源（按优先级排序）

1. **mirror.ghproxy.com**
   - 地址: `https://mirror.ghproxy.com/`
   - 特点: 稳定性好，速度快
   - 维护: 社区维护

2. **ghproxy.net**
   - 地址: `https://ghproxy.net/`
   - 特点: 响应速度快
   - 维护: 个人维护

3. **gh-proxy.com**
   - 地址: `https://gh-proxy.com/`
   - 特点: 可靠性高
   - 维护: 商业维护

4. **github.moeyy.xyz**（仅AppImageTool）
   - 地址: `https://github.moeyy.xyz/`
   - 特点: 专门的GitHub代理
   - 维护: 个人维护

5. **GitHub直连**（备用）
   - 地址: `https://github.com/`
   - 特点: 官方源，但可能无法访问
   - 维护: GitHub官方

## 🛠️ 下载策略

### 自动切换机制

我们的脚本采用了智能的下载策略：

1. **按优先级尝试**: 从国内镜像开始，逐个尝试
2. **超时控制**: 每个源30-60秒超时
3. **重试机制**: 每个源最多重试2次
4. **失败处理**: 自动清理不完整文件
5. **成功检测**: 验证下载完整性

### 下载流程

```bash
# 示例：AppImageTool下载流程
for url in "${DOWNLOAD_URLS[@]}"; do
    echo "尝试从以下地址下载: $url"
    
    if wget --timeout=30 --tries=2 -O "$APPIMAGETOOL_PATH" "$url"; then
        DOWNLOAD_SUCCESS=true
        break
    fi
    
    echo "从此源下载失败，尝试下一个源..."
    rm -f "$APPIMAGETOOL_PATH"  # 清理不完整文件
done
```

## 🔧 手动配置

### 如果自动下载失败

1. **检查网络连接**
   ```bash
   ping -c 3 mirror.ghproxy.com
   ```

2. **设置代理（如果有）**
   ```bash
   export http_proxy=http://proxy:port
   export https_proxy=http://proxy:port
   ```

3. **手动下载文件**
   
   **AppImageTool:**
   ```bash
   # 下载到指定位置
   wget -O ~/appImages/appimagetool-x86_64.AppImage \
     https://mirror.ghproxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage
   ```
   
   **Python便携版:**
   ```bash
   # 在构建目录中下载
   cd build_appimage_temp  # 或 build_minimal_temp
   wget -O python-portable.tar.gz \
     https://mirror.ghproxy.com/https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz
   ```

### 自定义镜像源

如果您有其他可用的镜像源，可以修改脚本中的URL数组：

```bash
# 在 setup_appimagetool.sh 中
DOWNLOAD_URLS=(
    "https://your-custom-mirror.com/path/to/appimagetool-x86_64.AppImage"
    # ... 其他源
)
```

## 📊 镜像源状态监控

### 检查镜像源可用性

```bash
# 检查AppImageTool镜像
curl -I https://mirror.ghproxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage

# 检查Python镜像
curl -I https://mirror.ghproxy.com/https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-pgo+lto-full.tar.gz
```

### 速度测试

```bash
# 测试下载速度
time wget --spider https://mirror.ghproxy.com/https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage
```

## ⚠️ 注意事项

### 镜像源限制

1. **带宽限制**: 某些镜像可能有带宽限制
2. **文件大小限制**: 大文件可能不被支持
3. **访问频率限制**: 避免过于频繁的请求

### 安全考虑

1. **文件完整性**: 下载后建议验证文件完整性
2. **来源可信**: 只使用可信的镜像源
3. **定期更新**: 镜像源地址可能会变化

### 故障排除

1. **DNS问题**: 尝试更换DNS服务器
2. **防火墙**: 检查防火墙设置
3. **代理设置**: 确认代理配置正确

## 🔄 更新维护

### 镜像源更新

我们会定期检查和更新镜像源列表，确保：

- 移除失效的镜像源
- 添加新的可靠镜像源
- 调整优先级顺序

### 版本兼容性

当Python或AppImageTool版本更新时，我们会：

- 更新下载链接
- 测试新版本兼容性
- 更新文档说明

## 📞 技术支持

如果您在使用过程中遇到下载问题：

1. **查看日志**: 检查脚本输出的错误信息
2. **网络诊断**: 测试网络连接和DNS解析
3. **手动验证**: 尝试手动下载文件
4. **反馈问题**: 向项目维护者报告问题

## 🎯 总结

通过配置多个国内镜像源，MemoCoco的AppImage构建过程在中国大陆地区的成功率和速度都得到了显著提升。自动切换机制确保了构建过程的稳定性，即使某个镜像源出现问题，也能自动切换到其他可用源。

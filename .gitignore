# IDE 配置
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Python 编译文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 打包相关
.pybuild/

# 特定项目文件
memococo/static/win/build/
memococo/static/win/dist/

# 日志文件
*.log

# 环境文件
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# AppImage 构建文件
AppDir/
build_appimage_temp/
build_minimal_temp/
*.AppImage
python-portable.tar.gz
python-minimal.tar.gz
appimagetool-x86_64.AppImage

# 测试结果
test_results.log

#!/bin/bash
# MemoCoco 极简 AppImage 打包脚本
# 作者: liuwenwu
# 日期: 2025-01-07
# 目标: 极致最小化体积

set -e  # 遇到错误立即退出

# 设置版本号和基本信息
VERSION="2.2.12"
APP_NAME="MemoCoco"
PYTHON_VERSION="3.11"

# 显示欢迎信息
echo "=================================================="
echo "  MemoCoco 极简 AppImage 打包脚本 v1.0"
echo "=================================================="
echo "当前版本: $VERSION"
echo "目标: 极致最小化体积"
echo "开始打包..."

# 检查必要的工具
echo "正在检查必要的工具..."
command -v wget >/dev/null 2>&1 || { echo "错误: 需要安装 wget"; exit 1; }
command -v python3 >/dev/null 2>&1 || { echo "错误: 需要安装 python3"; exit 1; }
command -v strip >/dev/null 2>&1 || { echo "错误: 需要安装 strip"; exit 1; }
command -v upx >/dev/null 2>&1 && UPX_AVAILABLE=true || UPX_AVAILABLE=false

if [ "$UPX_AVAILABLE" = true ]; then
    echo "检测到 UPX，将使用它进一步压缩二进制文件"
else
    echo "未检测到 UPX，建议安装以获得更好的压缩效果: sudo apt-get install upx"
fi

# 清理旧的打包文件
echo "正在清理旧的打包文件..."
rm -rf AppDir ${APP_NAME}-${VERSION}-minimal-x86_64.AppImage build_minimal_temp

# 创建临时构建目录
BUILD_DIR="build_minimal_temp"
mkdir -p $BUILD_DIR
cd $BUILD_DIR

# 下载最小化的Python构建
echo "正在下载最小化Python构建..."

# 定义最小化Python下载源（使用最新版本）
PYTHON_MINIMAL_FILENAME="cpython-3.11.13+20250712-x86_64-unknown-linux-gnu-install_only_stripped.tar.gz"
PYTHON_MINIMAL_URLS=(
    # 直连GitHub（新的仓库地址）
    "https://github.com/astral-sh/python-build-standalone/releases/download/20250712/$PYTHON_MINIMAL_FILENAME"
    # 国内镜像源
    "https://ghproxy.net/https://github.com/astral-sh/python-build-standalone/releases/download/20250712/$PYTHON_MINIMAL_FILENAME"
    "https://gh-proxy.com/https://github.com/astral-sh/python-build-standalone/releases/download/20250712/$PYTHON_MINIMAL_FILENAME"
)

if [ ! -f "python-minimal.tar.gz" ]; then
    echo "开始下载最小化Python..."
    PYTHON_MINIMAL_DOWNLOAD_SUCCESS=false

    for url in "${PYTHON_MINIMAL_URLS[@]}"; do
        echo "尝试从: $url"
        if wget --timeout=60 --tries=2 -O python-minimal.tar.gz "$url"; then
            PYTHON_MINIMAL_DOWNLOAD_SUCCESS=true
            echo "最小化Python下载成功"
            break
        fi
        echo "此源下载失败，尝试下一个..."
        rm -f python-minimal.tar.gz
    done

    if [ "$PYTHON_MINIMAL_DOWNLOAD_SUCCESS" = false ]; then
        echo "错误: 最小化Python下载失败"
        echo "请检查网络连接或手动下载文件到当前目录"
        exit 1
    fi
else
    echo "最小化Python文件已存在，跳过下载"
fi

# 创建 AppDir 结构
echo "正在创建 AppDir 结构..."
mkdir -p AppDir/usr/bin AppDir/usr/lib AppDir/usr/share/applications AppDir/usr/share/icons/hicolor/128x128/apps

# 解压最小化Python
echo "正在解压最小化Python..."
mkdir -p AppDir/usr/lib/python3.11
tar -xzf python-minimal.tar.gz -C AppDir/usr/lib/python3.11 --strip-components=1

# 极致清理Python安装
echo "=================================================="
echo "执行极致Python清理..."
echo "=================================================="

PYTHON_DIR="AppDir/usr/lib/python3.11"
PYTHON_EXEC="$PYTHON_DIR/bin/python3"
PIP_TARGET="$PYTHON_DIR/lib/python3.11/site-packages"

# 删除所有不必要的Python模块
echo "删除不必要的Python标准库模块..."
cd $PYTHON_DIR/lib/python3.11

# 删除大型不必要模块
rm -rf test tests __pycache__ 2>/dev/null || true
rm -rf ensurepip idlelib tkinter turtle* 2>/dev/null || true
rm -rf distutils lib2to3 pydoc_data 2>/dev/null || true
rm -rf multiprocessing/dummy concurrent/futures/thread.py 2>/dev/null || true

# 删除文档和示例
rm -rf ../../share/doc ../../share/man 2>/dev/null || true

# 返回构建目录
cd ../../../../../../

# 安装最小依赖集
echo "安装最小依赖集..."

# 只安装绝对必要的包
echo "安装Flask核心..."
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location --no-deps \
    Flask==3.0.3 \
    Werkzeug==3.0.3 \
    Jinja2==3.1.4 \
    MarkupSafe==2.1.5 \
    click==8.1.7 \
    itsdangerous==2.2.0

echo "安装系统工具..."
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location --no-deps \
    mss==9.0.1 \
    toml==0.10.2 \
    requests==2.32.3 \
    psutil==5.8.0

echo "安装轻量级图像处理..."
# 使用最小的图像处理库
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location \
    Pillow==10.4.0

echo "安装轻量级OCR..."
# 使用最小的OCR库
$PYTHON_EXEC -m pip install --target $PIP_TARGET --no-warn-script-location \
    rapidocr-onnxruntime==1.3.24

# 极致清理依赖
echo "=================================================="
echo "执行极致依赖清理..."
echo "=================================================="

SITE_PACKAGES="$PIP_TARGET"

# 删除所有测试和文档
find $SITE_PACKAGES -name "test*" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "*test*" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "docs" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "examples" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 删除所有缓存和编译文件
find $PYTHON_DIR -name "*.pyc" -delete 2>/dev/null || true
find $PYTHON_DIR -name "*.pyo" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "*.dist-info" -type d -exec rm -rf {} + 2>/dev/null || true
find $SITE_PACKAGES -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null || true

# 删除文档文件
find $SITE_PACKAGES -name "*.md" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "*.rst" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "*.txt" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "LICENSE*" -delete 2>/dev/null || true
find $SITE_PACKAGES -name "COPYING*" -delete 2>/dev/null || true

# 压缩二进制文件
echo "压缩二进制文件..."
find $PYTHON_DIR -name "*.so*" -exec strip {} + 2>/dev/null || true
find $PYTHON_DIR -type f -executable -exec strip {} + 2>/dev/null || true

# 如果有UPX，进一步压缩
if [ "$UPX_AVAILABLE" = true ]; then
    echo "使用UPX进一步压缩..."
    find $PYTHON_DIR -name "*.so*" -exec upx --best {} + 2>/dev/null || true
    upx --best $PYTHON_DIR/bin/python3 2>/dev/null || true
fi

echo "清理后Python目录大小:"
du -sh $PYTHON_DIR

# 复制应用程序文件（最小化版本）
echo "复制最小化应用程序文件..."
mkdir -p AppDir/usr/share/memococo

# 只复制必要的Python文件
cp -r ../../memococo/*.py AppDir/usr/share/memococo/ 2>/dev/null || true
cp -r ../../memococo/templates AppDir/usr/share/memococo/ 2>/dev/null || true
cp -r ../../memococo/static AppDir/usr/share/memococo/ 2>/dev/null || true
cp -r ../../memococo/i18n AppDir/usr/share/memococo/ 2>/dev/null || true
cp -r ../../memococo/common AppDir/usr/share/memococo/ 2>/dev/null || true

# 清理应用程序文件
find AppDir/usr/share/memococo -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find AppDir/usr/share/memococo -name "*.pyc" -delete 2>/dev/null || true

echo "应用程序文件大小:"
du -sh AppDir/usr/share/memococo

# 创建Python启动脚本
echo "创建Python启动脚本..."
cat > AppDir/usr/bin/python3 << 'EOF'
#!/bin/bash
APPDIR="$(dirname "$(dirname "$(readlink -f "${0}")")")"
export PYTHONPATH="$APPDIR/usr/share/memococo:$APPDIR/usr/lib/python3.11/lib/python3.11/site-packages:$PYTHONPATH"
export PYTHONHOME="$APPDIR/usr/lib/python3.11"
export LD_LIBRARY_PATH="$APPDIR/usr/lib/python3.11/lib:$LD_LIBRARY_PATH"
exec "$APPDIR/usr/lib/python3.11/bin/python3" "$@"
EOF
chmod +x AppDir/usr/bin/python3

# 创建主启动脚本
echo "创建主启动脚本..."
cat > AppDir/usr/bin/memococo << 'EOF'
#!/bin/bash
APPDIR="$(dirname "$(dirname "$(readlink -f "${0}")")")"

# 设置环境变量
export PYTHONPATH="$APPDIR/usr/share/memococo:$APPDIR/usr/lib/python3.11/lib/python3.11/site-packages:$PYTHONPATH"
export PYTHONHOME="$APPDIR/usr/lib/python3.11"
export LD_LIBRARY_PATH="$APPDIR/usr/lib/python3.11/lib:$LD_LIBRARY_PATH"

# 设置数据目录
export MEMOCOCO_DATA_DIR="${HOME}/.local/share/MemoCoco"
mkdir -p "$MEMOCOCO_DATA_DIR"

# 启动应用程序
echo "Starting MemoCoco application..."
cd "$MEMOCOCO_DATA_DIR"
exec "$APPDIR/usr/lib/python3.11/bin/python3" -m memococo.app
EOF
chmod +x AppDir/usr/bin/memococo

# 创建AppRun脚本
echo "创建AppRun脚本..."
cat > AppDir/AppRun << 'EOF'
#!/bin/bash
APPDIR="$(dirname "$(readlink -f "${0}")")"
exec "$APPDIR/usr/bin/memococo" "$@"
EOF
chmod +x AppDir/AppRun

# 创建桌面文件
echo "创建桌面文件..."
cat > AppDir/memococo.desktop << 'EOF'
[Desktop Entry]
Name=MemoCoco
Comment=时间胶囊 - 自动记录屏幕和文本
Exec=memococo
Icon=memococo
Terminal=false
Type=Application
Categories=Utility;
StartupNotify=true
EOF

# 复制图标
echo "复制图标..."
cp ../../memococo/static/favicon144x144.png AppDir/memococo.png
cp ../../memococo/static/favicon144x144.png AppDir/usr/share/icons/hicolor/128x128/apps/memococo.png

# 最终优化
echo "=================================================="
echo "执行最终优化..."
echo "=================================================="

# 最终清理
find AppDir -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find AppDir -name "*.pyc" -delete 2>/dev/null || true
find AppDir -name "*.pyo" -delete 2>/dev/null || true

# 显示最终大小
echo "最终AppDir大小:"
du -sh AppDir

# 检查appimagetool
APPIMAGETOOL_PATH="$HOME/appImages/appimagetool-x86_64.AppImage"
if [ -f "$APPIMAGETOOL_PATH" ]; then
    cp "$APPIMAGETOOL_PATH" ./appimagetool-x86_64.AppImage
    chmod +x appimagetool-x86_64.AppImage
elif [ -f "appimagetool-x86_64.AppImage" ]; then
    chmod +x appimagetool-x86_64.AppImage
else
    echo "错误: 未找到 appimagetool-x86_64.AppImage"
    exit 1
fi

# 创建AppImage
echo "创建极简AppImage..."
ARCH=x86_64 ./appimagetool-x86_64.AppImage AppDir ../${APP_NAME}-${VERSION}-minimal-x86_64.AppImage

# 返回原目录
cd ..

# 检查结果
if [ -f "${APP_NAME}-${VERSION}-minimal-x86_64.AppImage" ]; then
    echo "=================================================="
    echo "极简AppImage打包成功！"
    echo "文件: ${APP_NAME}-${VERSION}-minimal-x86_64.AppImage"
    echo "文件大小: $(du -h ${APP_NAME}-${VERSION}-minimal-x86_64.AppImage | cut -f1)"
    echo "=================================================="

    # 与标准版本比较
    if [ -f "${APP_NAME}-${VERSION}-x86_64.AppImage" ]; then
        STANDARD_SIZE=$(du -b ${APP_NAME}-${VERSION}-x86_64.AppImage | cut -f1)
        MINIMAL_SIZE=$(du -b ${APP_NAME}-${VERSION}-minimal-x86_64.AppImage | cut -f1)
        REDUCTION=$((($STANDARD_SIZE - $MINIMAL_SIZE) * 100 / $STANDARD_SIZE))
        echo "与标准版本相比，体积减少了约 ${REDUCTION}%"
    fi

    echo "=================================================="
else
    echo "极简AppImage打包失败！"
    exit 1
fi

# 清理临时文件
rm -rf $BUILD_DIR
echo "极简AppImage打包完成。"

# MemoCoco AppImage 打包指南

本文档介绍如何将MemoCoco项目打包为AppImage格式的可执行文件。

## 📦 AppImage 简介

AppImage是一种Linux应用程序的便携格式，具有以下优势：
- **一次构建，到处运行**: 在任何Linux发行版上都能运行
- **无需安装**: 直接下载运行，无需root权限
- **自包含**: 包含所有依赖，不会与系统冲突
- **便携性**: 可以放在U盘中随身携带

## 🛠️ 打包方案

我们提供了两个优化的AppImage打包方案：

### 1. 标准优化方案 - `build_appimage.sh` (推荐)

**特点:**
- 嵌入优化的Python运行时 (PGO+LTO)
- 完全自包含，无外部Python依赖
- 体积优化 (~400-500MB)
- 保留所有功能，兼容性最好

**技术细节:**
- 使用PGO+LTO优化的Python构建
- 轻量级依赖包选择 (如opencv-python-headless)
- 全面清理测试文件和文档
- 二进制文件压缩优化

**适用场景:**
- 生产环境发布 (推荐)
- 需要完整功能
- 平衡体积和功能

### 2. 极简方案 - `build_appimage_minimal.sh`

**特点:**
- 最小化Python运行时
- 只包含核心依赖
- 极致体积优化 (~300-400MB)
- 可能缺少某些高级功能

**技术细节:**
- 最小化Python标准库
- 只安装绝对必要的依赖
- UPX压缩 (如果可用)
- 极致清理和优化

**适用场景:**
- 对体积要求极高
- 网络带宽受限环境
- 嵌入式或资源受限系统

**使用方法:**
```bash
# 标准优化版本 (推荐)
./build_appimage.sh

# 极简版本
./build_appimage_minimal.sh
```

## 🔧 构建环境要求

### 系统要求
- Ubuntu 18.04+ / Debian 10+ 或兼容发行版
- Python 3.8+
- 至少2GB可用磁盘空间
- 网络连接（下载依赖）

### 必需工具
```bash
# 安装基础工具
sudo apt-get update
sudo apt-get install python3 python3-pip python3-venv wget curl build-essential

# 安装系统依赖
sudo apt-get install libgl1-mesa-glx libglib2.0-0 x11-utils xdotool ffmpeg
```

### 网络环境配置

**国内用户优势：**
- ✅ 已配置多个国内镜像源
- ✅ 自动切换下载源
- ✅ 显著提升下载速度

**支持的镜像源：**
- mirror.ghproxy.com
- ghproxy.net
- gh-proxy.com
- GitHub直连（备用）

## 📋 构建步骤

### 1. 准备环境
```bash
# 克隆项目
git clone https://github.com/liuwenwu520/MemoCoco.git
cd MemoCoco

# 确保所有依赖已安装
pip3 install -r requirements.txt
```

### 2. 设置 AppImageTool
```bash
# 自动下载并设置 AppImageTool
./setup_appimagetool.sh

# 或者手动下载到指定位置
mkdir -p ~/appImages
wget -O ~/appImages/appimagetool-x86_64.AppImage \
  https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage
chmod +x ~/appImages/appimagetool-x86_64.AppImage
```

### 3. 执行打包脚本
```bash
# 标准优化方案 (推荐)
./build_appimage.sh

# 极简方案 (最小体积)
./build_appimage_minimal.sh
```

### 3. 验证构建结果
```bash
# 检查生成的AppImage文件
ls -lh MemoCoco-*.AppImage

# 测试运行
chmod +x MemoCoco-2.2.11-x86_64.AppImage
./MemoCoco-2.2.11-x86_64.AppImage
```

## 🎯 使用生成的AppImage

### 基本使用
```bash
# 赋予执行权限
chmod +x MemoCoco-2.2.11-x86_64.AppImage

# 直接运行
./MemoCoco-2.2.11-x86_64.AppImage

# 或者双击运行（在图形界面中）
```

### 系统集成
```bash
# 将AppImage移动到应用程序目录
sudo mv MemoCoco-2.2.11-x86_64.AppImage /opt/

# 创建桌面快捷方式
cat > ~/.local/share/applications/memococo.desktop << EOF
[Desktop Entry]
Name=MemoCoco
Comment=时间胶囊 - 自动记录屏幕和文本
Exec=/opt/MemoCoco-2.2.11-x86_64.AppImage
Icon=memococo
Terminal=false
Type=Application
Categories=Utility;Office;
EOF
```

### 数据目录
- **配置文件**: `~/.local/share/MemoCoco/`
- **截图数据**: `~/.local/share/MemoCoco/screenshots/`
- **数据库**: `~/.local/share/MemoCoco/memococo.db`

## 🔍 故障排除

### 常见问题

#### 1. 权限错误
```bash
# 确保AppImage有执行权限
chmod +x MemoCoco-*.AppImage
```

#### 2. 缺少系统依赖
```bash
# 安装必要的系统库
sudo apt-get install libgl1-mesa-glx libglib2.0-0 x11-utils xdotool ffmpeg
```

#### 3. Python版本不兼容
```bash
# 检查Python版本
python3 --version

# 确保版本 >= 3.8
```

#### 4. 网络连接问题
```bash
# 检查网络连接
ping -c 3 github.com

# 如果在代理环境下，设置代理
export http_proxy=http://proxy:port
export https_proxy=http://proxy:port
```

### 调试模式
```bash
# 在终端中运行以查看详细输出
./MemoCoco-2.2.11-x86_64.AppImage

# 检查日志文件
tail -f ~/.local/share/MemoCoco/memococo.log
```

## 📊 性能特点

| 特性 | 标准优化方案 | 极简方案 |
|------|-------------|----------|
| 构建时间 | ~15-20分钟 | ~10-15分钟 |
| 包大小 | ~400-500MB | ~300-400MB |
| 启动时间 | ~2-3秒 | ~1-2秒 |
| 兼容性 | 最佳 | 良好 |
| 功能完整性 | 100% | ~90% |
| Python依赖 | 无需系统Python | 无需系统Python |
| 推荐度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🚀 发布建议

### 用户发布
- 使用**完整嵌入方案**提供最佳的用户体验
- 无需用户安装Python环境
- 最大兼容性，可在各种Linux发行版上运行
- 真正的"下载即用"体验

### 版本管理
```bash
# 为不同版本创建不同的AppImage
MemoCoco-2.2.11-x86_64.AppImage
MemoCoco-2.2.12-x86_64.AppImage
```

## 📝 注意事项

1. **构建环境**: 建议在干净的Ubuntu环境中构建
2. **依赖管理**: 确保requirements.txt包含所有必要依赖
3. **测试验证**: 在不同的Linux发行版上测试AppImage
4. **文件大小**: 注意AppImage文件大小，避免过大影响分发
5. **更新机制**: AppImage本身不支持自动更新，需要手动下载新版本

## 🔗 相关链接

- [AppImage官方文档](https://appimage.org/)
- [PyInstaller文档](https://pyinstaller.readthedocs.io/)
- [MemoCoco项目主页](https://github.com/liuwenwu520/MemoCoco)
- [UmiOCR项目](https://github.com/hiroi-sora/Umi-OCR)

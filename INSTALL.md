# MemoCoco 安装说明

## 使用 AppImage (推荐)

AppImage是一种便携的Linux应用程序格式，无需安装即可运行：

1. 下载最新的 AppImage 文件：`MemoCoco-2.2.11-x86_64.AppImage`
2. 赋予执行权限：
   ```bash
   chmod +x MemoCoco-2.2.11-x86_64.AppImage
   ```
3. 直接运行：
   ```bash
   ./MemoCoco-2.2.11-x86_64.AppImage
   ```
   或者双击文件直接运行

## AppImage 优势

- **无需安装**：下载即用，无需root权限
- **便携性强**：可以放在U盘中随身携带
- **自包含**：包含所有依赖，不会与系统冲突
- **兼容性好**：在任何Linux发行版上都能运行

## 系统集成（可选）

如果想将AppImage集成到系统中：

1. 移动到应用程序目录：
   ```bash
   sudo mv MemoCoco-2.2.11-x86_64.AppImage /opt/
   ```

2. 创建桌面快捷方式：
   ```bash
   cat > ~/.local/share/applications/memococo.desktop << EOF
   [Desktop Entry]
   Name=MemoCoco
   Comment=时间胶囊 - 自动记录屏幕和文本
   Exec=/opt/MemoCoco-2.2.11-x86_64.AppImage
   Icon=memococo
   Terminal=false
   Type=Application
   Categories=Utility;Office;
   EOF
   ```

## 数据存储

- **配置文件**: `~/.local/share/MemoCoco/`
- **截图数据**: `~/.local/share/MemoCoco/screenshots/`
- **数据库**: `~/.local/share/MemoCoco/memococo.db`

## 卸载方法

删除AppImage文件即可：

```bash
rm MemoCoco-2.2.11-x86_64.AppImage
```

如果想完全删除所有数据：

```bash
rm -rf ~/.local/share/MemoCoco
```

## 构建 AppImage

如果你想自己构建AppImage，可以使用项目提供的打包脚本：

```bash
# 首先设置 AppImageTool (已配置国内镜像源)
./setup_appimagetool.sh

# 选择打包方案:
# 标准优化版本 (推荐)
./build_appimage.sh

# 极简版本 (最小体积)
./build_appimage_minimal.sh
```

### 网络环境说明

构建脚本已配置多个国内镜像源，确保在中国大陆地区稳定下载：

- **mirror.ghproxy.com** - 主要镜像源
- **ghproxy.net** - 备用镜像源
- **gh-proxy.com** - 备用镜像源
- **GitHub直连** - 最后备用

如果遇到下载问题，请参考 [国内镜像源配置说明.md](国内镜像源配置说明.md)

详细的构建说明请参考：[docs/appimage_packaging.md](docs/appimage_packaging.md)
